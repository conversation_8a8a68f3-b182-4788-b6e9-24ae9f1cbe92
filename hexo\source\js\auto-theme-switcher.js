// 自动主题切换器 - 根据时间自动切换主题
class AutoThemeSwitcher {
  constructor() {
    this.config = {
      enabled: true,
      lightStartHour: 6,  // 早上6点开始浅色模式
      darkStartHour: 18,  // 晚上6点开始深色模式
      checkInterval: 5 * 60 * 1000, // 每5分钟检查一次
      manualOverrideDuration: 2 * 60 * 1000 // 手动切换后2分钟内不自动切换
    };
    
    this.storageKeys = {
      theme: 'theme',
      manualOverride: 'theme_manual_override',
      manualTime: 'theme_manual_time',
      autoSwitched: 'theme_auto_switched'
    };
    
    this.isInitialized = false;
    console.log('🎨 自动主题切换器初始化...');
  }

  // 初始化
  init() {
    if (this.isInitialized) {
      console.log('⚠️ 自动主题切换器已初始化');
      return;
    }

    console.log('🚀 启动自动主题切换器...');

    // 延迟初始化，确保页面完全加载
    setTimeout(() => {
      this.initTheme();
      this.startPeriodicCheck();
      this.createStatusIndicator();
      this.setupEventListeners();
      this.isInitialized = true;
      console.log('✅ 自动主题切换器初始化完成');
    }, 1000);
  }

  // 初始化主题
  initTheme() {
    console.log('🎨 初始化主题设置...');
    
    if (!this.config.enabled) {
      console.log('⏱️ 自动主题切换已禁用');
      return;
    }

    // 检查是否有手动切换的记录
    const manualOverride = this.isManualOverrideActive();
    
    if (manualOverride) {
      console.log('👤 保持手动设置的主题');
    } else {
      this.autoSwitchThemeByTime();
    }
  }

  // 检查是否在手动切换的有效期内
  isManualOverrideActive() {
    const manualOverride = localStorage.getItem(this.storageKeys.manualOverride) === 'true';
    const manualTime = parseInt(localStorage.getItem(this.storageKeys.manualTime) || '0');
    const now = Date.now();
    
    if (manualOverride && (now - manualTime > this.config.manualOverrideDuration)) {
      console.log('⏱️ 手动切换已超过2分钟，重新启用自动切换');
      this.clearManualOverride();
      return false;
    }
    
    return manualOverride;
  }

  // 清除手动切换标记
  clearManualOverride() {
    localStorage.removeItem(this.storageKeys.manualOverride);
    localStorage.removeItem(this.storageKeys.manualTime);
  }

  // 根据时间自动切换主题
  autoSwitchThemeByTime() {
    if (!this.config.enabled) {
      return;
    }

    const now = new Date();
    const currentHour = now.getHours();
    
    // 判断当前应该使用的主题
    let targetTheme;
    if (currentHour >= this.config.lightStartHour && currentHour < this.config.darkStartHour) {
      targetTheme = 'light';
    } else {
      targetTheme = 'dark';
    }

    // 获取当前主题
    const currentTheme = this.getCurrentTheme();

    // 如果当前主题与目标主题不同，则切换
    if (currentTheme !== targetTheme) {
      const timeDesc = targetTheme === 'dark' ? '深色' : '浅色';
      console.log(`⏱️ 基于时间自动切换主题: ${currentHour}时，切换到${timeDesc}模式`);
      this.setTheme(targetTheme, true);
    }
  }

  // 获取当前主题
  getCurrentTheme() {
    return document.documentElement.getAttribute('data-theme') || 'light';
  }

  // 设置主题
  setTheme(theme, isAutoSwitch = false) {
    try {
      // 尝试调用现有的主题切换函数
      if (typeof sco !== 'undefined' && sco.switchDarkMode && theme !== this.getCurrentTheme()) {
        sco.switchDarkMode();
      } else {
        // 备用方案：直接设置主题
        document.documentElement.setAttribute('data-theme', theme);
        
        if (theme === 'dark') {
          document.body.classList.add('dark-mode');
        } else {
          document.body.classList.remove('dark-mode');
        }

        // 保存主题设置
        localStorage.setItem(this.storageKeys.theme, theme);
        
        if (isAutoSwitch) {
          localStorage.setItem(this.storageKeys.autoSwitched, 'true');
        }
      }

      console.log(`✅ 主题已${isAutoSwitch ? '自动' : '手动'}设置为: ${theme}`);
      
      // 触发主题切换事件
      this.dispatchThemeChangeEvent(theme, isAutoSwitch);
      
    } catch (error) {
      console.error('❌ 设置主题失败:', error);
    }
  }

  // 手动切换主题
  toggleTheme() {
    console.log('🎨 手动切换主题');

    const currentTheme = this.getCurrentTheme();
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    // 设置主题
    this.setTheme(newTheme, false);
    
    // 标记为手动切换
    this.setManualOverride();
  }

  // 设置手动切换标记
  setManualOverride() {
    try {
      localStorage.setItem(this.storageKeys.manualOverride, 'true');
      localStorage.setItem(this.storageKeys.manualTime, Date.now().toString());
      console.log('👤 已标记为手动切换主题');
    } catch (error) {
      console.warn('⚠️ 保存手动切换标记失败:', error);
    }
  }

  // 启动定期检查
  startPeriodicCheck() {
    setInterval(() => {
      if (!this.isManualOverrideActive()) {
        this.autoSwitchThemeByTime();
      }
    }, this.config.checkInterval);
    
    console.log(`⏰ 定期检查已启动，间隔: ${this.config.checkInterval / 60000}分钟`);
  }

  // 触发主题切换事件
  dispatchThemeChangeEvent(theme, isAutoSwitch) {
    const event = new CustomEvent('themeChanged', {
      detail: {
        theme: theme,
        isAutoSwitch: isAutoSwitch,
        timestamp: Date.now()
      }
    });

    document.dispatchEvent(event);

    // 显示切换通知
    this.showThemeNotification(theme, isAutoSwitch);
  }

  // 显示主题切换通知
  showThemeNotification(theme, isAutoSwitch) {
    // 移除现有通知
    const existingNotification = document.querySelector('.theme-switch-notification');
    if (existingNotification) {
      existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'theme-switch-notification';

    const themeText = theme === 'dark' ? '深色模式' : '浅色模式';
    const switchType = isAutoSwitch ? '自动切换到' : '手动切换到';
    const icon = theme === 'dark' ? '🌙' : '☀️';

    // 如果是手动切换，添加额外提示
    const extraInfo = isAutoSwitch ? '' : '<div style="font-size: 12px; opacity: 0.8; margin-top: 4px;">2分钟后恢复自动切换</div>';

    notification.innerHTML = `
      <span>${icon} ${switchType}${themeText}</span>
      ${extraInfo}
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.classList.add('show');
    }, 100);

    // 自动隐藏（手动切换时显示更长时间）
    const displayTime = isAutoSwitch ? 3000 : 4500;
    setTimeout(() => {
      notification.classList.remove('show');
      notification.classList.add('hide');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, displayTime);
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 自动主题切换器配置已更新:', this.config);
  }

  // 获取当前状态
  getStatus() {
    const now = new Date();
    const currentHour = now.getHours();
    const isManualOverride = this.isManualOverrideActive();
    
    return {
      enabled: this.config.enabled,
      currentTheme: this.getCurrentTheme(),
      currentHour: currentHour,
      isManualOverride: isManualOverride,
      nextSwitchTime: this.getNextSwitchTime(),
      config: this.config
    };
  }

  // 获取下次切换时间
  getNextSwitchTime() {
    const now = new Date();
    const currentHour = now.getHours();
    
    let nextSwitchHour;
    if (currentHour < this.config.lightStartHour) {
      nextSwitchHour = this.config.lightStartHour;
    } else if (currentHour < this.config.darkStartHour) {
      nextSwitchHour = this.config.darkStartHour;
    } else {
      nextSwitchHour = this.config.lightStartHour + 24; // 明天的浅色模式时间
    }
    
    const nextSwitch = new Date(now);
    nextSwitch.setHours(nextSwitchHour % 24, 0, 0, 0);
    
    if (nextSwitchHour >= 24) {
      nextSwitch.setDate(nextSwitch.getDate() + 1);
    }
    
    return nextSwitch;
  }

  // 创建状态指示器
  createStatusIndicator() {
    // 移除现有指示器
    const existingIndicator = document.querySelector('.theme-status-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    // 创建状态指示器
    const indicator = document.createElement('div');
    indicator.className = 'theme-status-indicator';
    indicator.title = '主题状态指示器';

    // 添加到页面
    document.body.appendChild(indicator);

    // 更新状态
    this.updateStatusIndicator();
  }

  // 更新状态指示器
  updateStatusIndicator() {
    const indicator = document.querySelector('.theme-status-indicator');
    if (!indicator) return;

    const isManualOverride = this.isManualOverrideActive();
    const currentTheme = this.getCurrentTheme();

    // 清除所有状态类
    indicator.classList.remove('auto', 'manual', 'light', 'dark');

    if (isManualOverride) {
      indicator.classList.add('manual');
      indicator.title = '手动模式 - 点击查看详情';
    } else {
      indicator.classList.add('auto', currentTheme);
      const nextSwitch = this.getNextSwitchTime();
      const timeStr = nextSwitch.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      indicator.title = `自动模式 - 下次切换: ${timeStr}`;
    }

    // 添加点击事件显示详细信息
    indicator.onclick = () => this.showStatusInfo();
  }

  // 显示状态信息
  showStatusInfo() {
    const status = this.getStatus();
    const nextSwitch = this.getNextSwitchTime();
    const timeStr = nextSwitch.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

    let message = `当前主题: ${status.currentTheme === 'dark' ? '深色模式' : '浅色模式'}\n`;
    message += `当前时间: ${status.currentHour}:00\n`;

    if (status.isManualOverride) {
      message += `状态: 手动模式\n`;
      message += `说明: 手动切换后2分钟内不会自动切换`;
    } else {
      message += `状态: 自动模式\n`;
      message += `浅色模式: ${this.config.lightStartHour}:00 - ${this.config.darkStartHour}:00\n`;
      message += `深色模式: ${this.config.darkStartHour}:00 - ${this.config.lightStartHour}:00\n`;
      message += `下次切换: ${timeStr}`;
    }

    alert(message);
  }

  // 设置事件监听器
  setupEventListeners() {
    // 监听主题切换事件
    document.addEventListener('themeChanged', () => {
      this.updateStatusIndicator();
    });

    // 监听页面可见性变化，页面重新可见时检查主题
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !this.isManualOverrideActive()) {
        setTimeout(() => {
          this.autoSwitchThemeByTime();
        }, 1000);
      }
    });
  }
}

// 创建全局实例
window.autoThemeSwitcher = new AutoThemeSwitcher();

// 确保在全局作用域中可用
window.AutoThemeSwitcher = AutoThemeSwitcher;

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.autoThemeSwitcher.init();
  });
} else {
  window.autoThemeSwitcher.init();
}
