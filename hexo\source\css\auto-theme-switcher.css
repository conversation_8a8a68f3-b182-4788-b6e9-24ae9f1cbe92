/* 自动主题切换器样式 */

/* 主题切换按钮增强样式 */
#theme_toggle_button {
  position: relative;
  transition: all 0.3s ease;
}

/* 主题切换按钮悬停效果 */
#theme_toggle_button:hover {
  transform: scale(1.05);
}

/* 主题切换动画 */
html {
  transition: background-color 0.5s ease, color 0.5s ease;
}

body {
  transition: background-color 0.5s ease, color 0.5s ease;
}

/* 主题切换提示 */
.theme-switch-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--efu-card-bg);
  color: var(--efu-fontcolor);
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  font-size: 14px;
  border-left: 4px solid var(--efu-theme);
  backdrop-filter: blur(10px);
}

.theme-switch-notification.show {
  opacity: 1;
  transform: translateX(0);
}

.theme-switch-notification.hide {
  opacity: 0;
  transform: translateX(100%);
}

/* 主题切换图标动画 */
#theme_toggle_button i {
  transition: transform 0.3s ease;
}

#theme_toggle_button:active i {
  transform: rotate(180deg);
}

/* 深色模式特定样式 */
[data-theme="dark"] .theme-switch-notification {
  background: rgba(44, 44, 44, 0.95);
  color: #e0e0e0;
  border-left-color: #ffc848;
}

/* 浅色模式特定样式 */
[data-theme="light"] .theme-switch-notification {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border-left-color: #425AEF;
}

/* 主题切换状态指示器 */
.theme-status-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  z-index: 1000;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.theme-status-indicator.auto {
  background: linear-gradient(45deg, #425AEF, #ffc848);
  animation: pulse 2s infinite;
}

.theme-status-indicator.manual {
  background: #ff6b6b;
}

.theme-status-indicator.light {
  background: #425AEF;
}

.theme-status-indicator.dark {
  background: #ffc848;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

/* 主题切换时的页面过渡效果 */
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-switch-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
    font-size: 13px;
    padding: 10px 16px;
  }
  
  .theme-switch-notification.show {
    transform: translateY(0);
  }
  
  .theme-switch-notification.hide {
    transform: translateY(-100%);
  }
  
  .theme-status-indicator {
    bottom: 10px;
    left: 10px;
    width: 10px;
    height: 10px;
  }
}

/* 主题切换按钮工具提示 */
#theme_toggle_button::after {
  content: attr(title);
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 10000;
}

#theme_toggle_button:hover::after {
  opacity: 1;
}

[data-theme="dark"] #theme_toggle_button::after {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

/* 主题切换时的平滑过渡 */
* {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease, 
              box-shadow 0.3s ease;
}

/* 避免某些元素的过渡效果 */
img, video, iframe, canvas, svg {
  transition: none !important;
}

/* 主题切换加载状态 */
.theme-switching {
  pointer-events: none;
}

.theme-switching * {
  transition-duration: 0.1s !important;
}

/* 自动切换时间显示 */
.auto-theme-info {
  position: fixed;
  bottom: 60px;
  left: 20px;
  background: var(--efu-card-bg);
  color: var(--efu-fontcolor);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 999;
  border: 1px solid var(--efu-border-color);
}

.auto-theme-info.show {
  opacity: 0.8;
  transform: translateY(0);
}

.auto-theme-info:hover {
  opacity: 1;
}

@media (max-width: 768px) {
  .auto-theme-info {
    bottom: 30px;
    left: 10px;
    right: 10px;
    text-align: center;
  }
}
