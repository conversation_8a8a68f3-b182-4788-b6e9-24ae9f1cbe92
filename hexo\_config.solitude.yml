# ---------------------------
# Hexo Theme Solitude
# Author: 伍十七(@everfu)
# Github: https://github.com/everfu/hexo-theme-solitude
#
# Guide: https://solitude.js.org
# You can get more detailed help from the guide
# 指南：https://solitude.js.org/zh
# 你可以从指南中获取更详细的帮助
#
# sponsor: https://ko-fi.com/everfu
# 赞助：https://afdian.com/a/everfu
# ---------------------------

### Basic configuration

# --------------------------- start ---------------------------
# Site information
# 网站信息
site:
  name:
    class: text # text / i_class / img
    custom: 主页 # Solitude / fas fa-ghost / /img/pwa/favicon.ico
  icon: /img/photo.jpg # Site icon / 网站图标
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Navigation bar
# 导航栏
nav:
  # Left Box
  # 左侧盒子
  group:
  #  project: # name
  #    Solitude: https://github.com/everfu/hexo-theme-solitude || /img/pwa/favicon.ico # name: url || icon

  # Menu
  # 菜单
  menu:
    首页: / # name: link
    文库: # name
      全部文章: /archives/ || fas fa-folder-closed # item name: link || icon
      全部分类: /categories/ || fas fa-clone
      全部标签: /tags/ || fas fa-tags
    工具:
      在线工具: /tools/ || fas fa-toolbox
    # 友链:
    #   朋友圈: /moments/ || fas fa-wifi
    #   友情链接: /links/ || fas fa-user-group
    #   宝藏博主: javascript:travelling() || fas fa-gift
    # 关于:
    #   我的装备: /equipment/ || fas fa-laptop
    #   音乐馆: /music/ || fas fa-music

  # Right button
  # 右侧按钮
  right:
    random: false # Random article / 随机文章按钮
    custom:
      - name: 主题切换 # 名字
        icon: fas fa-palette # 图标
        onclick: toggleTheme() # 点击事件
        id: theme_toggle_button # id
    #  - name: 开往 # 名字
    #    url: https://www.travellings.cn/go.html # 跳转链接
    #    icon: fas fa-train # 图标
    #    onclick: # 点击事件
    #    id: travellings_button # id
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Home Top Banner
# 首页顶部
hometop:
  enable: true
  banner:
    title: 欢迎来到我的小站 # 大字
    desc:
      分享生活的人 # 小字
      # - 我只是一个普通的程序员
      # - 但我有一个不平凡的梦想
      # - 我希望能够改变世界
    icon:
      HTML: # name
        img: https://i.postimg.cc/vBWVnY8q/html.png # url
        color: "#e9572b" # color
      JS:
        img: https://i.postimg.cc/3N10Ltv2/js.png
        color: "#f7cb4f"
      Docker:
        img: https://i.postimg.cc/8Pk6Fg24/docker.png
        color: "#57b6e6"
      Flutter:
        img: https://i.postimg.cc/hPC7T3gB/flutter.png
        color: "#ffffff"
      WebPack:
        img: https://i.postimg.cc/dVLZBmtT/webpack.png
        color: "#2e3a41"
      Git:
        img: https://i.postimg.cc/nhgjwjCS/git.png
        color: "#df5b40"
  group:
    # 热门: /tags/Fire/ || fas fa-fire || linear-gradient(to right,#f65,#ffbf37)
  recommendList:
    enable: true
    sup: 置顶
    title: Solitude 官方文档
    url: https://solitude.js.org/
    img: /img/default.avif
    color: "none"

# Article Top Banner
# 文章推荐轮播图
carousel: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Aside
# 侧边栏
aside:
  # Values: about (info card), newestPost (latest article), allInfo (website information), newest_comment (latest comment)
  # 值: about(信息卡), newestPost(最新文章), allInfo(网站信息), newest_comment(最新评论)

  # Sticky: Fixed position / noSticky: Not fixed position
  # Sticky: 固定位置 / noSticky: 不固定位置
  home: # on the homepage
    noSticky: "about"
    Sticky: "allInfo"
  post: # on the article page
    noSticky: "about"
    Sticky: "newestPost"
  page: # on the page
    noSticky: "about"
    Sticky: "newestPost,allInfo"
  # 菜单栏位置(0: 左 1: 右)
  position: 0 # Sidebar positioning(0: left 1: right)

  # --------------------------- start ---------------------------
  # Information card
  # 信息卡
  my_card:
    author:
      img: /img/photo.jpg # url
      sticker: # url, 24x24 size
    # 介绍 / Introduction
    description: 心是无限幕布，世界皆为舞台。
    # 内容 / Content
    content: # 这是我的博客 / This is my Blog
    state:
      morning: ✨ 早上好，新的一天开始了
      noon: 🍲 午餐时间
      afternoon: 🌞 下午好
      night: 早点休息
      goodnight: 晚安 😴
    witty_words:
      # - 你可以的
      # - 你一定可以的
      # - 祝你好运，陌生人
    # social
    # 社交信息图标
    information:
    #  Github: https://github.com/everfu || fab fa-github # Name: link || icon
    #  Bilibili: https://space.bilibili.com/1329819902 || fab fa-bilibili
  # --------------------------- end ---------------------------

  # --------------------------- start ---------------------------
  # article table of contents
  # 文章目录
  toc:
    post: true
    page: false
    vague: true
  # --------------------------- end ---------------------------

  # --------------------------- start ---------------------------
  # Tags
  # 标签
  tags:
    enable: false
    limit: 20 # Number of tags displayed
    # Highlighted tags
    highlight_list:
      # - Hexo
  # --------------------------- end ---------------------------

  # --------------------------- start ---------------------------
  # Site Info
  # 网站信息
  siteinfo:
    # Number of articles
    # 文章数量
    postcount: true
    # Total number of words
    # 总字数
    wordcount: false
    # PV
    pv: true
    # UV
    uv: true
    # Last update date
    # 最后更新日期
    updatetime: true
    # Website creation time
    # 网站创建时间
    runtimeenable: true
    # Format: yyyy-MM-dd hh-mm-ss
    # 格式: yyyy-MM-dd hh-mm-ss
    runtime: "2023-04-20 00:00:00"
  # --------------------------- end ---------------------------
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Page default settings
# 页面默认设置
page:
  # 404 page
  # 404 页面
  error: true
  # Tags page
  # 标签页面
  tags: true
  # Categories page
  # 分类页面
  categories: true
  # list: Sort List / 1: Follow HomeList
  # list: 排序列表 / 1: 跟随首页列表
  archives: 0
  # Default value
  # 默认值
  default:
    # Default image when no cover is set
    # 未设置封面时的默认图片
    cover:
      # - /img/default.png # 默认图片 / default cover
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Post default settings
# 文章默认设置
post:
  default:
    # Default image when no cover is set
    # 未设置封面时的默认图片
    cover:
      # -  # 默认图片 / default cover
    # Location
    # 位置
    locate: China
    # Copyright
    # 版权
    copyright:
      enable: true
      author: /img/logo.png # url
      # License
      # 许可证
      license: CC BY-NC-SA 4.0
      # License link
      # 许可证链接
      licenurl: https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh-hans
  # Article Local AI
  # 文章本地AI
  ai:
    enable: false
    modelName: 小七 GPT
  # Article meta information
  # 文章元信息
  meta:
    # Release date
    # 发布日期
    date: false
    # Update date
    # 更新日期
    updated: false
    # Location
    # 位置
    locate: false
    # Number of words
    # 字数
    wordcount: false
    # uv
    readtime: false
    # pv
    pv: false
    # Comment count
    # 评论数
    comment: false
  # Reward
  # 打赏
  award:
    enable: false
    appreciators: /about/ # Reward page
    # Reward Title
    # 打赏标题
    title: # Thanks for your appreciation. / 感谢您的赞赏
    desc: # Because of your support, I realize the value of writing articles. / 由于您的支持，我才能够实现写作的价值。
    # Reward list
    # 打赏列表
    list:
      # - name: Github Sponsor
      #   qcode: https://s3.qjqq.cn/47/661ba900c4bc1.webp!color
      #   url: https://github.com/sponsors/everfu
      #   color: var(--efu-black)

  # Share icon
  # 分享图标
  share:
    enable: false
    list:
      # - qq
      # - weibo
      # - twitter
      # - facebook
      # - telegram
      # - whatsapp
      # - linkedin
      # - link
      # - qrcode
  rss: # /atom.xml
  # Article reading progress
  # 文章封面取色
  covercolor:
    enable: false
    # local: local color / api: api color / ave: oss average color
    mode: local
    # api address / api 地址
    api: https://api.qjqq.cn/api/Imgcolor?img=
    # Storage / 缓存时间
    time: 43200000
  footer:
    enable: false
    desc: # Articles from Ever Fu. / 文章来自 Ever Fu # description
    button: # Button
      enable: true
      name: # Learn more / 了解更多
      url: /about/
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Custom Theme Color
theme_color:
  dark: "#ffc848" # dark
  light: "#425AEF" # light
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# display mode
# 显示模式
display_mode:
  # auto: automatic switching(Recognize the current theme mode of the device) / dark: dark mode / light: light mode
  # auto: 自动切换（识别设备当前主题模式） / dark: 深色模式 / light: 浅色模式
  type: auto
  # After opening, the dark mode will display the starry sky background
  # 开启后深色模式会显示星空背景
  universe: false
  # Auto switch theme based on time
  # 根据时间自动切换主题
  auto_time:
    enable: true
    # Light mode start time (hour, 24-hour format)
    # 浅色模式开始时间（小时，24小时制）
    light_start: 6
    # Dark mode start time (hour, 24-hour format)
    # 深色模式开始时间（小时，24小时制）
    dark_start: 18
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Font
# 字体
font:
  font-size: 16px
  code-font-size: 16px
  # Global font
  # 全局字体
  font-family: "PingFang SC, Hiragino Sans GB, Microsoft YaHei, sans-serif"
  # Code font
  # 代码字体
  code-font-family: '"monospace", monospace'
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Background
# 背景图片
background:
  enable: false
  opacity: .2
  dark: https://i.pinimg.com/originals/d8/b3/9d/d8b39d12b653810db452c437211aeb2e.png
  light: https://i.pinimg.com/originals/93/57/38/935738ed9657b296c2ef0ebd2151eb66.jpg
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Lure
# When the user exits the page, modify the title
# 当用户退出页面时，修改标题
lure:
  enable: false
  jump: 404 Not Found
  back: ヾ(≧∇≦*)ゝHey, hey, you fell for it.
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Article expiration
# 文章过期
expire:
  enable: false
  time: 30 # days
  position: top # top / bottom
  text_prev: "This article expired "
  text_next: " day ago, if the content does not match, please contact the webmaster to update it."
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Home page article configuration information
# 首页文章配置信息
index_post_list:
  direction: column # row / column
  column: 2 # 2: 2 columns 3: 3 columns
  cover: both
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Related articles
# 相关文章
related_post:
  enable: false
  limit: 2
  # created: release date / updated: update date
  # created: 发布日期 / updated: 更新日期
  date_type: created
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Custom right menu
# 自定义右键菜单
right_menu:
  enable: false
  # Whether to display the hot comment switch.
  # 是否显示热门评论开关
  commentBarrage: false
  # Whether to display the browser's context menu when hold Ctrl key.
  # 是否在按住 Ctrl 键时显示浏览器右键菜单
  ctrlOriginalMenu: false
  # Simplified and Traditional Chinese translation.
  # 简繁体转换
  translate: false
  # Custom list
  # 自定义列表
  custom_list:
    # - name: 随机文章
    #   click: toRandomPost()
    #   id: menu-randomPost
    #   class:
    #   icon: fas fa-tower-broadcast
    # - name: 全部分类
    #   click: pjax.loadUrl('/categories/') # External links with window.open, pjax can not request cross-domain content.
    #   id:
    #   class:
    #   icon: fas fa-clone
    # - name: 全部标签
    #   click: pjax.loadUrl('/tags/')
    #   id:
    #   class:
    #   icon: fas fa-tags
# --------------------------- end -----------------------

# --------------------------- start ---------------------------
# Copy
#  information
# 复制信息
copy:
  enable: false
  # Turn on Link copyright information after copying.
  # 复制后链接版权信息
  copyright:
    enable: false
    # Display when the number of words copied exceeds
    # 复制文字时超过多少字数显示
    limit: 50
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Mermaid
mermaid: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Chart.js
chart: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# typeit
typeit: false
# --------------------------- end ---------------------------

### Extended configuration

# --------------------------- start ---------------------------
# Console
# 控制台
console:
  enable: false
  # Recent comments
  # 最新评论
  recentComment:
    enable: false
    # Cache time 1: 1 day / .5 : half a day
    # 缓存时间 1: 1天 / .5 : 半天
    storage: .2
  card:
    # Tags
    # 标签
    tags: true
    # Archives
    # 归档
    archive: "month" # month: by month / year: by year
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
translate:
  enable: true
  defaultEncoding: 2 # 1: 默认繁体 2: 默认简体
  translateDelay: 0 # 首次加载翻译迟疑时间
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Right-Sidebar
# 右下角悬停导航栏
rightside:
  enable: true
  percent: false
  hide:
    enable: false
    translate: false
    mode: false
    aside: false
# --------------------------- start ---------------------------

# --------------------------- start ---------------------------
# Footer
# 页脚
footer:
  # 社交图标
  information:
    author: false # img url / false
    left:
      # Github: https://github.com/everfu || fab fa-github # 名称: 链接 || 图标
      # Mail: mailto:<EMAIL> || far fa-envelope
    right:
      # Bilibili: https://space.bilibili.com/1329819902 || fab fa-bilibili
      # Douyin: https://v.douyin.com/iJsLc8jt/ || fab fa-tiktok
  # 友情链接
  group:
    # 导航:
    #   Archives: /archives/
    #   Categories: /categories/
    #   Tags: /tags/
    # 排列:
    #   Cookies: /cookies/
    #   Privacy: /privacy/
    #   Copyright: /copyright/

  # 随机友链
  randomlink: false # 随机友链

  # 备案
  beian:
    # - name: 湘公网安备43048102000175号
    #   icon: https://beian.mps.gov.cn/img/logo01.dd7ff50e.png
    #   url: https://beian.mps.gov.cn/#/query/webSearch
    # - name: 湘ICP备**********号-1
    #   url: https://beian.miit.gov.cn/

  # 页脚信息文字
  links:
    # - name: RSS
    #   url: /atom.xml
    # - name: License
    #   url: https://github.com/everfu/hexo-theme-solitude/blob/main/LICENSE
    #   icon:
    #     - fas fa-copyright
    #     - fab fa-creative-commons-by
    #     - fab fa-creative-commons-nc
    #     - fab fa-creative-commons-nd
    # - name: boringbay
    #   url: https://boringbay.com/
    #   img: https://boringbay.com/api/badge/www.efu.me
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 404 page
# 404 页面
errorpage:
  img: /img/404.avif
  text: =awa= Page Not Found # Text
  recommendList: true
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Meting API
# This part of the content uses metingjs,
# can only use NetEase Cloud Music, QQ Music and other music platforms supported by the mainland China region,
# the subsequent consideration of the use of JSON files to store music information and customize the implementation of the third-party API does not depend on the page.
# Music Page
meting_api: "https://meting.qjqq.cn/?server=:server&type=:type&id=:id&auth=:auth&r=:r" # Custom API
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Capsule music
# 音乐胶囊
capsule:
  enable: false
  # 歌单 ID / 单曲 ID
  id: 5144842535
  # 服务商：netease / qq / xiami / kugou / baidu
  server: netease
  # 类型：playlist / song
  type: playlist
  volume: 0.8
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Quick Menu
# Open with shift + ?
# 快捷菜单
# 使用 shift + ? 打开
keyboard:
  enable: false
  list:
    # - name: 关闭快捷菜单
    #   key: K
    #   func: keyboard
    # - name: 打开控制台
    #   key: A
    #   sco: showConsole
    # - name: 播放/暂停音乐
    #   key: M
    #   sco: musicToggle
    # - name: 打开友链
    #   key: L
    #   url: /links/
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Lazyload
# 图片懒加载
lazyload:
  enable: false
  # post, site
  field: site
  # 加载时替换图
  placeholder: ""
  # 加载失败替换图
  errorimg: /img/error_load.avif
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Loading
# 加载
loading:
  # Full screen loading
  # 全屏加载
  fullpage: false
  # Loading icon, default is siteicon
  # 加载图标，不写默认siteicon
  favicon: /img/favicon.png
  # Pace loading
  # Pace 加载
  pace: true
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Highlight
# 代码块高亮
highlight:
  enable: true
  # Display the fold button when the number of words exceeds
  # 当超过多少字时显示折叠按钮
  limit: 200
  # Whether to enable the copy button
  # 是否启用复制按钮
  copy: true
  # Whether to expand by default
  # 是否默认展开
  expand: true
  # default: default / mac : apple terminal
  # default: 默认 / mac : 苹果终端
  theme: mac
  # default / solidity / dracula
  color: default
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Lightbox
# 图片灯箱
lightbox: false
# warning: Please select any one, but cannot be turned on at the same time.
# 警告: 请任选其一，但不能同时开启。
fancybox: false # fancybox
mediumZoom: false # mediumZoom
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Memorial
# Turn the entire site gray on memorable days.
# 在纪念日整个网站变灰
memorial:
  enable: false
  date:
  #  - 7-7
  #  - 9-18
  #  - 12-13
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# OpenGraph
OpenGraph:
  enable: false
  options:
    # twitter_card:
    # twitter_image:
    # twitter_id:
    # twitter_site:
    # google_plus:
    # fb_admins:
    # fb_app_id:
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Word count
# 字数统计
# warning: Please install the hexo-wordcount plugin first.
# 警告: 请先安装 hexo-wordcount 插件。
wordcount: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Katex
# Latex formula support
# Latex 公式支持
katex:
  enable: false
  # Whether to load on each page
  # 是否在每个页面加载
  per_page: false
  # Whether to enable copy formula
  # 是否启用复制公式
  copytex: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# verification
# 验证
verify_site:
  # - name: google-site-verification
  #   content: xxxxxx
  # - name: baidu-site-verification
  #   content: xxxxxxx
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# CSS Prefix
# CSS 前缀
# When turned on, it will automatically prefix the CSS (to get better browser support), but this will increase the size of the CSS file.
# 开启后会自动给 CSS 加前缀（以获得更好的浏览器支持），但这会增加 CSS 文件的大小。
css_prefix: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Extend
# 扩展
extends:
  # Insert in head
  # 插入到 head
  head:
    - <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    - <link rel="stylesheet" href="/css/custom.css">
    - <link rel="stylesheet" href="/css/mobile-fix-gentle.css">
    - <link rel="stylesheet" href="/css/banner-clock.css">
    - <link rel="stylesheet" href="/css/auto-theme-switcher.css">
    - <script src="/js/weather-manager.js" defer></script>
    - <script src="/js/banner-clock.js" defer></script>
    - <script src="/js/auto-theme-switcher.js" defer></script>

  # Insert in body
  # 插入到 body
  body:
    - |
      <script>
      // 初始化横幅时钟（独立组件）- 增强版
      function initBannerClock() {
        console.log('🎯 开始初始化横幅时钟...');
        console.log('🔍 当前页面URL:', window.location.href);
        console.log('🔍 当前页面路径:', window.location.pathname);

        // 清理可能存在的旧时钟实例
        if (window.bannerClockInstance) {
          if (window.bannerClockInstance.destroy) {
            window.bannerClockInstance.destroy();
          }
          window.bannerClockInstance = null;
          console.log('🗑️ 清理旧的横幅时钟实例');
        }

        // 移除可能存在的旧DOM元素
        const existingClock = document.getElementById('banner-clock');
        if (existingClock) {
          existingClock.remove();
          console.log('�️ 移除旧的横幅时钟DOM');
        }

        // 等待一小段时间，确保DOM完全加载
        setTimeout(() => {
          // 检查banners容器是否存在且可见
          const bannersContainer = document.getElementById('banners');

          console.log('🔍 DOM检查结果:');
          console.log('- banners容器:', bannersContainer ? '✅ 存在' : '❌ 不存在');

          // 检查容器是否可见的辅助函数
          function isContainerVisible(element) {
            if (!element) return false;
            const style = window.getComputedStyle(element);
            return style.display !== 'none' && style.visibility !== 'hidden' && element.offsetParent !== null;
          }

          // 只在首页banners容器存在且可见时才显示时钟
          if (!bannersContainer || !isContainerVisible(bannersContainer)) {
            console.log('📍 banners容器不存在或不可见，跳过时钟初始化（仅首页显示）');
            return;
          }

          const targetContainer = bannersContainer;
          const containerType = 'banners';
          console.log('📍 使用banners容器（首页模式）');

          // 创建新的横幅时钟DOM
          const bannerClock = document.createElement('div');
          bannerClock.id = 'banner-clock';
          bannerClock.setAttribute('data-container', containerType);
          bannerClock.innerHTML = `
            <div class="banner-clock-time">加载中...</div>
            <div class="banner-clock-date">加载中...</div>
            <div class="banner-clock-weather">
              <div class="banner-clock-weather-item">
                <i class="fas fa-cloud-sun"></i>
                <span>获取天气中...</span>
              </div>
              <div class="banner-clock-weather-item">
                <i class="fas fa-tint"></i>
                <span>--</span>
              </div>
            </div>
            <div class="banner-clock-location">
              <i class="fas fa-map-marker-alt"></i>
              <span>获取位置中...</span>
            </div>
          `;

          targetContainer.appendChild(bannerClock);
          console.log(`📍 横幅时钟DOM已创建并添加到${containerType}容器`);

          // 初始化横幅时钟组件（独立）
          setTimeout(() => {
            if (typeof BannerClockWidget !== 'undefined') {
              window.bannerClockInstance = new BannerClockWidget();
              console.log('🚀 横幅时钟组件已独立初始化');
            } else {
              console.warn('⚠️ BannerClockWidget类未找到');
            }
          }, 300);
        }, 100); // 等待DOM稳定
      }

      // 侧边栏时钟已移除 - 避免移动端布局问题

      // 初始化天气管理器（全局单例）
      function initWeatherManager() {
        if (!window.weatherManager && typeof WeatherManager !== 'undefined') {
          console.log('🌤️ 创建天气管理器实例...');
          window.weatherManager = new WeatherManager();

          // 延迟初始化，确保DOM完全加载
          setTimeout(() => {
            console.log('🚀 开始初始化天气管理器...');
            window.weatherManager.init();
            console.log('✅ 全局天气管理器已初始化');
          }, 1000);

          return true;
        } else if (window.weatherManager && !window.weatherManager.isInitialized) {
          // 如果实例存在但未初始化，则初始化
          setTimeout(() => {
            console.log('🔄 重新初始化天气管理器...');
            window.weatherManager.init();
          }, 1000);
          return true;
        }
        return !!window.weatherManager;
      }

      // 创建全局位置选择器（只创建一次）
      function createGlobalLocationSelector() {
        if (!document.getElementById('location-selector-modal')) {
          const locationModal = document.createElement('div');
          locationModal.id = 'location-selector-modal';
          locationModal.innerHTML = `
            <div class="location-modal-content">
              <div class="location-modal-header">
                <h3>选择城市</h3>
                <button class="location-close-btn">&times;</button>
              </div>
              <div class="location-modal-body">
                <div class="location-search">
                  <input type="text" id="city-search-input" placeholder="搜索城市...">
                  <div id="city-suggestions" class="city-suggestions"></div>
                </div>
                <div class="popular-cities">
                  <h4>热门城市</h4>
                  <div class="city-buttons">
                    <button class="city-btn" data-city="北京">北京</button>
                    <button class="city-btn" data-city="上海">上海</button>
                    <button class="city-btn" data-city="广州">广州</button>
                    <button class="city-btn" data-city="深圳">深圳</button>
                    <button class="city-btn" data-city="杭州">杭州</button>
                    <button class="city-btn" data-city="南京">南京</button>
                    <button class="city-btn" data-city="成都">成都</button>
                    <button class="city-btn" data-city="重庆">重庆</button>
                  </div>
                </div>
              </div>
              <div class="location-modal-footer">
                <button id="location-cancel-btn" class="btn-cancel">取消</button>
                <button id="location-confirm-btn" class="btn-confirm" disabled>确认</button>
              </div>
            </div>
          `;
          document.body.appendChild(locationModal);
          console.log('📍 全局位置选择器已创建');
        }
      }

      // 时钟健康检查和恢复机制
      function ensureClockExists() {
        const clock = document.getElementById('banner-clock');
        if (!clock && !window.clockInitializing) {
          console.warn('⚠️ 时钟丢失，执行恢复初始化...');
          window.clockInitializing = true;

          // 多重尝试策略
          let attempts = 0;
          const maxAttempts = 3;

          function tryInitClock() {
            attempts++;
            console.log(`🔄 尝试初始化时钟 (第${attempts}次)`);

            initBannerClock();

            // 检查初始化是否成功
            setTimeout(() => {
              const newClock = document.getElementById('banner-clock');
              if (!newClock && attempts < maxAttempts) {
                console.warn(`⚠️ 第${attempts}次初始化失败，重试...`);
                setTimeout(tryInitClock, 500 * attempts); // 递增延迟
              } else if (newClock) {
                console.log('✅ 时钟恢复成功');
                window.clockInitializing = false;
              } else {
                console.error('❌ 时钟恢复失败，已达到最大尝试次数');
                window.clockInitializing = false;
              }
            }, 1000);
          }

          tryInitClock();
        }
      }

      // 独立初始化函数
      function initializeClockSystem() {
        console.log('🚀 开始初始化时钟系统...');

        // 1. 初始化天气管理器
        const weatherManagerReady = initWeatherManager();

        // 2. 创建全局位置选择器
        createGlobalLocationSelector();

        // 3. 独立初始化横幅时钟
        initBannerClock();

        console.log('✅ 时钟系统初始化完成');

        // 4. 启动健康检查（每5秒检查一次）
        if (!window.clockHealthCheck) {
          window.clockHealthCheck = setInterval(ensureClockExists, 5000);
          console.log('🔍 时钟健康检查已启动');
        }
      }

      // 页面加载完成后立即初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM加载完成，开始初始化时钟系统');
        initializeClockSystem();
      });

      // 页面完全加载后再次确保
      window.addEventListener('load', function() {
        console.log('📄 页面完全加载，再次确保时钟系统');
        setTimeout(initializeClockSystem, 100);
      });

      // PJAX支持 - 增强版页面导航处理
      if (typeof pjax !== 'undefined') {
        // PJAX开始时清理旧的组件实例
        document.addEventListener('pjax:start', function() {
          console.log('🔄 PJAX导航开始，清理旧组件...');

          // 清理横幅时钟实例
          if (window.bannerClockInstance) {
            // 调用destroy方法清理所有资源
            if (window.bannerClockInstance.destroy) {
              window.bannerClockInstance.destroy();
            }
            window.bannerClockInstance = null;
            console.log('🗑️ 横幅时钟实例已清理');
          }

          // 移除旧的横幅时钟DOM元素
          const oldBannerClock = document.getElementById('banner-clock');
          if (oldBannerClock) {
            oldBannerClock.remove();
            console.log('🗑️ 旧的横幅时钟DOM已移除');
          }
        });

        // PJAX完成后重新初始化时钟系统
        document.addEventListener('pjax:complete', function() {
          console.log('✅ PJAX导航完成，重新初始化时钟系统...');
          console.log('🔍 当前页面URL:', window.location.href);
          console.log('🔍 当前页面路径:', window.location.pathname);

          // 等待DOM稳定后检查
          setTimeout(() => {
            const bannersExists = !!document.getElementById('banners');
            const bodyExists = !!document.body;
            console.log('🔍 DOM状态检查:');
            console.log('- banners容器:', bannersExists ? '✅ 存在' : '❌ 不存在');
            console.log('- body元素:', bodyExists ? '✅ 存在' : '❌ 不存在');
            console.log('- 文档就绪状态:', document.readyState);

            if (bodyExists) {
              console.log('⏰ 开始初始化时钟系统...');
              initializeClockSystem();
            } else {
              console.error('❌ DOM未就绪，跳过初始化');
            }
          }, 100);

          // 第一次保险检查
          setTimeout(() => {
            const clock = document.getElementById('banner-clock');
            if (!clock) {
              console.warn('⚠️ 第一次检查：时钟未找到，执行保险初始化...');
              initBannerClock();
            } else {
              console.log('✅ 第一次检查：时钟存在');
            }
          }, 500);

          // 第二次保险检查
          setTimeout(() => {
            const clock = document.getElementById('banner-clock');
            if (!clock) {
              console.warn('⚠️ 第二次检查：时钟仍未找到，强制初始化...');
              // 强制清理并重建
              window.clockInitializing = false;
              ensureClockExists();
            } else {
              console.log('✅ 第二次检查：时钟正常');
            }
          }, 1500);
        });

        // PJAX错误处理
        document.addEventListener('pjax:error', function() {
          console.warn('⚠️ PJAX导航出错，尝试重新初始化时钟系统...');
          setTimeout(() => {
            initializeClockSystem();
          }, 500);
        });
      }

      // 手动切换主题函数
      function toggleTheme() {
        console.log('🎨 手动切换主题');

        // 使用自动主题切换器的方法
        if (window.autoThemeSwitcher) {
          window.autoThemeSwitcher.toggleTheme();
        } else {
          // 备用方案：直接切换主题
          const currentTheme = document.documentElement.getAttribute('data-theme');
          const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

          document.documentElement.setAttribute('data-theme', newTheme);
          if (newTheme === 'dark') {
            document.body.classList.add('dark-mode');
          } else {
            document.body.classList.remove('dark-mode');
          }

          // 保存主题设置
          try {
            localStorage.setItem('theme', newTheme);
          } catch (error) {
            console.warn('⚠️ 保存主题设置失败:', error);
          }

          console.log('✅ 主题已切换到:', newTheme);
        }
      }

      // 确保函数在全局作用域中可用
      window.toggleTheme = toggleTheme;
      </script>
    - |
      <!-- 位置选择器模态框 -->
      <div id="location-selector-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 9999; backdrop-filter: blur(5px);">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 24px; width: 90%; max-width: 500px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);">
          <h3 style="margin: 0 0 20px 0; text-align: center; color: #333; font-size: 1.2rem;">选择您的位置</h3>

          <!-- 搜索框 -->
          <div style="margin-bottom: 20px;">
            <input type="text" id="city-search-input" placeholder="搜索城市名称..." style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box; outline: none; transition: border-color 0.3s;">
            <div id="city-suggestions" style="display: none; position: absolute; background: white; border: 1px solid #e0e0e0; border-radius: 8px; margin-top: 4px; max-height: 200px; overflow-y: auto; width: calc(100% - 48px); z-index: 10000; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);"></div>
          </div>

          <!-- 热门城市 -->
          <div style="margin-bottom: 20px;">
            <p style="margin: 0 0 12px 0; color: #666; font-size: 14px;">热门城市：</p>
            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
              <button class="city-btn" data-city="北京市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">北京</button>
              <button class="city-btn" data-city="上海市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">上海</button>
              <button class="city-btn" data-city="广州市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">广州</button>
              <button class="city-btn" data-city="深圳市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">深圳</button>
              <button class="city-btn" data-city="杭州市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">杭州</button>
              <button class="city-btn" data-city="南京市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">南京</button>
              <button class="city-btn" data-city="成都市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">成都</button>
              <button class="city-btn" data-city="武汉市" style="padding: 8px 16px; border: 1px solid #e0e0e0; background: white; border-radius: 20px; cursor: pointer; font-size: 13px; transition: all 0.3s; color: #333;">武汉</button>
            </div>
          </div>

          <!-- 按钮 -->
          <div style="display: flex; gap: 12px; justify-content: flex-end;">
            <button id="location-cancel-btn" style="padding: 10px 20px; border: 1px solid #e0e0e0; background: white; border-radius: 6px; cursor: pointer; color: #666; font-size: 14px; transition: all 0.3s;">取消</button>
            <button id="location-confirm-btn" disabled style="padding: 10px 20px; border: none; background: #2196f3; color: white; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; opacity: 0.5;">确认</button>
          </div>
        </div>
      </div>

      <style>
      /* 位置选择器样式 */
      #city-search-input:focus {
        border-color: #2196f3 !important;
      }

      .city-btn:hover {
        background: #f5f5f5 !important;
        border-color: #2196f3 !important;
        color: #2196f3 !important;
      }

      .city-btn.selected {
        background: #2196f3 !important;
        color: white !important;
        border-color: #2196f3 !important;
      }

      .city-suggestion {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.3s;
      }

      .city-suggestion:hover {
        background: #f5f5f5;
      }

      .city-suggestion:last-child {
        border-bottom: none;
      }

      #location-confirm-btn:not(:disabled) {
        opacity: 1 !important;
        cursor: pointer !important;
      }

      #location-confirm-btn:not(:disabled):hover {
        background: #1976d2 !important;
      }

      /* 深色模式适配 */
      [data-theme="dark"] #location-selector-modal > div {
        background: #2c2c2c !important;
        color: #e0e0e0 !important;
      }

      [data-theme="dark"] #location-selector-modal h3 {
        color: #e0e0e0 !important;
      }

      [data-theme="dark"] #city-search-input {
        background: #3c3c3c !important;
        border-color: #555 !important;
        color: #e0e0e0 !important;
      }

      [data-theme="dark"] #city-suggestions {
        background: #3c3c3c !important;
        border-color: #555 !important;
      }

      [data-theme="dark"] .city-btn {
        background: #3c3c3c !important;
        border-color: #555 !important;
        color: #e0e0e0 !important;
      }

      [data-theme="dark"] .city-btn:hover {
        background: #4c4c4c !important;
      }

      [data-theme="dark"] .city-suggestion {
        border-color: #555 !important;
      }

      [data-theme="dark"] .city-suggestion:hover {
        background: #4c4c4c !important;
      }

      [data-theme="dark"] #location-cancel-btn {
        background: #3c3c3c !important;
        border-color: #555 !important;
        color: #e0e0e0 !important;
      }
      </style>
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# PWA
# Progressive Web App
pwa:
  enable: false
  manifest: /manifest.json # manifest.json
  theme_color: "#006a73" # Theme color
  mask_icon: /img/pwa/favicon.png # Mask icon
  apple_touch_icon: /img/pwa/favicon.png # Apple touch icon
  bookmark_icon: /img/pwa/favicon.png # Bookmark icon
  favicon_32_32: /img/pwa/favicon_32.png # 32x32 icon
  favicon_16_16: /img/pwa/favicon_16.png # 16x16 icon
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Comment
# 评论
comment:
  # Which commenting system to use(e.g. waline or waline,twikoo)
  # 使用哪个评论系统（例如： waline or waline,twikoo）
  use: # waline, twikoo, valine, artalk, giscus # Up to two comment systems can be turned on at the same time
  # Whether to display the hot comment switch.
  # 是否显示热门评论开关
  commentBarrage: false
  # Lazy load
  # 懒加载评论区
  lazyload: false
  # Whether to display the comment count
  # 显示评论数
  count: false
  # Sidebar displays the total number of comments
  # Sidebar 显示总评论数
  sidebar: false
  # PV
  pv: false
  # Avatar
  avatar: https://gravatar.com/avatar
  # Hot comment tips
  # 热评提示
  hot_tip:
    enable: false
    # Number of hot comments
    count: 3
  # recent comments(⚠️ Comments need to be configured first.)
  # 最新评论(⚠️ 需要先配置评论)
  newest_comment:
    enable: true
    storage: .5 # 缓存时间 1: 1天 / .5 : 半天 / Cache time 1: 1 day .5 : half a day
    limit: 5 # 评论数 / Number of comments
# Twikoo: https://solitude.js.org/comment/twikoo
twikoo: # https://twikoo.js.org/
  envId: # url: https://twikoo.sondy.top/
  region: # Environment locale, default is ap-shanghai, Tencent cloud environment fill ap-shanghai or ap-guangzhou; Vercel environment do not fill the.
  style: true # Use custom styles when turned on
  accessToken: # AccessToken
  option: # twikoo option
# Waline: https://solitude.js.org/comment/waline
waline: # https://waline.js.org/
  envId: # url: https://waline.wzsco.top
  pageview: false # Whether to enable page access statistics
  option: # waline configuration item
# Valine: https://solitude.js.org/comment/valine
valine:
  appId: # leancloud application app id
  appKey: # leancloud application app key
  serverURLs: # This configuration is suitable for domestic custom domain name users, overseas version will be automatically detected (no need to manually fill in)
  avatar: # https://valine.js.org/avatar.html
  visitor: false
  style: true # Use custom styles when turned on
  option: # options list
# Artalk: https://solitude.js.org/comment/artalk
# Artalk: https://solitude.js.org/zh/comment/artalk
artalk:
  server: # server url
  site: # site name
  option: # options
# Giscus: https://solitude.js.org/comment/giscus
giscus:
  repo: # GitHub repository name
  repo_id: # GitHub repository ID
  category_id: # GitHub repository category ID
  theme:
    light: light
    dark: dark
  option:
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Search
search:
  enable: true
  type: local # local / algolia / docsearch
  tags:
  
  # Algolia
  algolia:
    # hits:
    #   per_page: 6

  # Local search
  local:
    preload: true
    CDN: # url: search.xml

  # DocSearch
  # https://docsearch.algolia.com/
  docsearch:
    appId:
    apiKey:
    indexName:
    option:
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Music Page
# 音乐馆
music:
  enable: false
  # 歌单 ID / 单曲 ID
  id: 5144842535
  # 服务商：netease / qq / xiami / kugou / baidu
  server: netease
  # 类型：playlist / song
  type: playlist
  # 默认音量
  volume: 0.8
  # 是否自动播放
  mutex: true
  # 播放方式：list / random
  order: list
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Danmaku Page
# 弹幕留言页面
envelope:
  enable: false
  line: 10 # 显示行数
  speed: 20 # 播放速度
  hover: true # 鼠标悬停暂停
  loop: true # 循环播放
  page: /message/ # 留言板页面 / message board page
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Brevity Page
# 即可短文
brevity:
  enable: false
  home_mini: false
  music: false
  page: /essay/
  style: 1
  strip: 30
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Recent comments Page
# 最近评论页面
recent_comments:
  enable: false
  limit: 50 # ⚠️waline 仅支持最大50条评论 / ⚠️waline only supports a maximum of 50 comments
  cache: 0.2 # 1 = 1天 / 1 = 1 day
  page: /recentcomments/ # 最近评论页面 / recent comments page
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Busuanzi
busuanzi: false
# 0: Original / 1: Custom
# 0: 原版 / 1: 自定义版
busuanzi_use: 0
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Google Adsense
# 谷歌广告
google_adsense:
  enable: false
  # Auto ads
  # 自动广告
  auto_ads: false
  # Page-level ads
  # 页面级广告
  enable_page_level_ads: true
  # Sidebar card ads
  # 侧边栏卡片广告
  aside_card: true
  # Post card ads
  # 文章卡片广告
  post_card: true
  # Post content ads
  # 文章内容广告
  post_content: true
  # Google Adsense js
  # 谷歌广告 js
  js: https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js
  client: # ca-pub-XXXXXXXXXXXXXX
  slot: # 4236388782
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 非必要请勿修改
CDN:
  internal: local # local / cdnjs / jsdelivr / unpkg / custom
  third_party: custom # cdnjs / jsdelivr / unpkg / custom
  version: true # 是否使用版本号
  custom_format: https://fastly.jsdelivr.net/npm/${name}@${version}/${min_file} # 自定义格式
  # 直接覆盖默认 CDN 链接（优先级最高）
  options:
    # algolia_search:
    # aplayer_css:
    # aplayer_js:
    # artalk_css:
    # artalk_js:
    # blueimp_md5:
    # busuanzi_js:
    # chart_js:
    # color_thief:
    # fancyapps_css:
    # fancyapps_ui:
    # fontawesome:
    # instantsearch:
    # katex:
    # katex_copytex:
    # lazyload:
    # medium_zoom:
    # mermaid_js:
    # meting_js:
    # pace_js:
    # pjax:
    # qrcode:
    # snackbar:
    # swiper_css:
    # swiper_js:
    # twikoo:
    # typeit_js:
    # valine:
    # waline_css:
    # waline_js:
# --------------------------- end ---------------------------
