/* 温和的移动端修复 - 补充custom.css的规则 */
@media (max-width: 768px) {
  /* 基础视口修复 */
  html, body {
    overflow-x: hidden;
    margin: 0;
    padding: 0;
  }

  /* 文章内容补充修复 */
  .post-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  /* 代码块修复 - 保持原有样式的同时确保不溢出 */
  .terminal-block,
  .highlight,
  pre {
    max-width: 100%;
    overflow-x: auto;
    box-sizing: border-box;
  }
  
  /* 图片修复 */
  .post-content img {
    max-width: 100%;
    height: auto;
  }
  
  /* 表格修复 */
  .post-content table {
    max-width: 100%;
    overflow-x: auto;
    display: block;
  }
  
  /* 横幅时钟移动端优化 */
  #banner-clock {
    width: 180px;
    min-height: 130px;
    height: auto !important;
    bottom: 15px;
    left: 15px;
    padding: 12px;
  }
  
  /* 位置选择器移动端优化 */
  #location-selector-modal > div {
    width: 95%;
    max-width: 95%;
    margin: 0 auto;
    box-sizing: border-box;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  #article-container {
    padding: 10px;
  }
  
  .post-content {
    font-size: 14px;
    line-height: 1.6;
  }
  
  .post-title {
    font-size: 1.5rem;
  }
  
  #banner-clock {
    width: 160px;
    min-height: 110px;
    height: auto !important;
    bottom: 10px;
    left: 10px;
    padding: 10px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  #banner-clock {
    width: 140px;
    min-height: 100px;
    height: auto !important;
    bottom: 10px;
    left: 10px;
  }
}

/* 移除了侧边栏完全隐藏规则，改为在custom.css中精确控制 */

@media (max-width: 768px) {
  .terminal-block {
    margin: 10px 0;
    font-size: 12px;
  }
  
  .terminal-header {
    padding: 6px 10px;
  }
  
  .terminal-title {
    font-size: 10px;
  }
  
  .terminal-btn {
    width: 10px;
    height: 10px;
  }
  
  .terminal-content {
    padding: 10px;
    font-size: 12px;
    min-height: 40px;
  }
  
  .copy-btn {
    padding: 3px 6px;
    font-size: 10px;
    right: 8px;
  }
}
