---
title: 参数保存
cover: https://s21.ax1x.com/2025/08/26/pVyR8yt.png
keywords: []
date: 2025-07-15 13:53:57
tags: [3XUI, Cloudflare, ArgoSB, 隧道, 代理]
categories: [技术教程]
description: 保存重要的服务器配置参数，包括3XUI面板地址、CF固定隧道密钥和ArgoSB脚本命令
password: 123456
---

<style>
.terminal-block {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    font-family: 'Courier New', monospace;
}

.terminal-header {
    background: linear-gradient(90deg, #3a3a3a 0%, #4a4a4a 100%);
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #555;
}

.terminal-title {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f57; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #28ca42; }

.terminal-content {
    padding: 15px;
    color: #00ff00;
    font-size: 14px;
    position: relative;
    background: #1e1e1e;
}

.terminal-prompt {
    color: #00ff00;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-btn:hover {
    background: #005a9e;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.textContent;
        button.textContent = '已复制';
        button.style.background = '#28a745';
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '#007acc';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
    });
}
</script>

## 1. ArgoSB一键无交互代理脚本

### 脚本命令

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">ArgoSB脚本</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>vmpt="" argo="y" agn="" agk="" bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh)
        <button class="copy-btn" onclick="copyTerminalContent('vmpt=&quot;&quot; argo=&quot;y&quot; agn=&quot;&quot; agk=&quot;&quot; bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh)', this)">复制</button>
    </div>
</div>

### AGSB快捷方式

#### 1. 查看Argo信息

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">查看Argo信息</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>agsb list
        <button class="copy-btn" onclick="copyTerminalContent('agsb list', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">查看Argo信息（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list
        <button class="copy-btn" onclick="copyTerminalContent('bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list', this)">复制</button>
    </div>
</div>

#### 2. 在线切换IPV4/IPV6节点配置（双栈VPS专享）

**显示IPV4节点配置：**

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV4节点配置</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=4 agsb list
        <button class="copy-btn" onclick="copyTerminalContent('ip=4 agsb list', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV4节点配置（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=4 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list
        <button class="copy-btn" onclick="copyTerminalContent('ip=4 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list', this)">复制</button>
    </div>
</div>

**显示IPV6节点配置：**

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV6节点配置</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=6 agsb list
        <button class="copy-btn" onclick="copyTerminalContent('ip=6 agsb list', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV6节点配置（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=6 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list
        <button class="copy-btn" onclick="copyTerminalContent('ip=6 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list', this)">复制</button>
    </div>
</div>

#### 3. 卸载脚本

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">卸载脚本</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>agsb del
        <button class="copy-btn" onclick="copyTerminalContent('agsb del', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">卸载脚本（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) del
        <button class="copy-btn" onclick="copyTerminalContent('bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) del', this)">复制</button>
    </div>
</div>

---

## ⚠️ 免责声明

本文档仅供学习和研究使用，请勿用于任何非法活动。使用者需遵守当地法律法规，作者不承担任何责任。请在24小时内删除相关内容。

---

## SAP CF APP保活脚本

为了确保SAP CF应用持续运行，可以使用GitHub Actions设置保活脚本。

### GitHub Actions 配置

将以下内容保存为 `.github/workflows/keepalive.yml` 文件：

```yaml
name: SAP CF APP保活

on:
  schedule:
    - cron: "*/5 0 * * *"
    - cron: "*/10 0 * * *"
    - cron: "*/15 0 * * *"
    - cron: "*/20 0 ** *"
    - cron: "*/30 * * * *"
  workflow_dispatch:

concurrency:
  group: keepalive
  cancel-in-progress: true

jobs:
  keepalive:
    runs-on: ubuntu-latest
    timeout-minutes: 8

    steps:
      - name: KeepAlive via container
        uses: docker://ghcr.io/uncleluogithub/cf-keepalive:latest
        env:
          CF_USERNAME: ${{ secrets.CF_USERNAME }}
          CF_PASSWORD: ${{ secrets.CF_PASSWORD }}
          CF_API:      ${{ secrets.CF_API }}
          CF_ORG:      ${{ secrets.CF_ORG }}
          CF_SPACE:    ${{ secrets.CF_SPACE }}
          CF_APP:      ${{ secrets.CF_APP }}
          CF_SET_PROCESS_HC: "true"
          # 调试时打开
          # DEBUG: "1"
          # CF_TRACE: "true"
```

### 所需 Secrets 参数

在GitHub仓库中添加以下6个Secrets：

*   **CF_API**: 登录网址
*   **CF_USERNAME**: 你的邮箱
*   **CF_PASSWORD**: 你的密码
*   **CF_ORG**: org
*   **CF_SPACE**: 空间名称
*   **CF_APP**: 项目名称

---

## SAP节点教程

### SPA新加坡教程

1.  **部署应用**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">部署新加坡应用</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf push qianxiusg --docker-image ghcr.io/uncleluogithub/sapcf:latest -m 512M -k 512M --health-check-type port --no-route --no-start
            <button class="copy-btn" onclick="copyTerminalContent('cf push qianxiusg --docker-image ghcr.io/uncleluogithub/sapcf:latest -m 512M -k 512M --health-check-type port --no-route --no-start', this)">复制</button>
        </div>
    </div>

2.  **设置环境参数**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">设置新加坡环境参数</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf set-env qianxiusg APP_UUID d2bb54ae-2568-4b03-975f-b2db6b05b56a<br>
            <span class="terminal-prompt">$</span>cf set-env qianxiusg VMESS_HOST spasg.0407123.xyz<br>
            <span class="terminal-prompt">$</span>cf set-env qianxiusg TUNNEL_TOKEN 'eyJhIjoiMTlmOGI1NWVlOGY3NjA4ZmY0YzdmZGY2OTM0YzdmZDciLCJ0IjoiZGI5ODlhNGEtMzBkOC00YTMxLTg5MGItMWQzNjBjOGMxOGE1IiwicyI6Ik1tRm1ZVEU1WkRJdFlXRTRNQzAwWXpjekxUbGhOV1l0TWprME1XUTRaR013TVRVMCJ9'
            <button class="copy-btn" onclick="copyTerminalContent('cf set-env qianxiusg APP_UUID d2bb54ae-2568-4b03-975f-b2db6b05b56a\ncf set-env qianxiusg VMESS_HOST spasg.0407123.xyz\ncf set-env qianxiusg TUNNEL_TOKEN \'eyJhIjoiMTlmOGI1NWVlOGY3NjA4ZmY0YzdmZGY2OTM0YzdmZDciLCJ0IjoiZGI5ODlhNGEtMzBkOC00YTMxLTg5MGItMWQzNjBjOGMxOGE1IiwicyI6Ik1tRm1ZVEU1WkRJdFlXRTRNQzAwWXpjekxUbGhOV1l0TWprME1XUTRaR013TVRVMCJ9\'', this)">复制</button>
        </div>
    </div>

3.  **确认端口健康检查类型**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">确认健康检查</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf set-health-check qianxiusg port
            <button class="copy-btn" onclick="copyTerminalContent('cf set-health-check qianxiusg port', this)">复制</button>
        </div>
    </div>

4.  **启动应用**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">启动新加坡应用</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf start qianxiusg
            <button class="copy-btn" onclick="copyTerminalContent('cf start qianxiusg', this)">复制</button>
</div>
    </div>

5.  **查询节点信息（日志信息查看）**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">查询新加坡节点信息</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf logs qianxiusg --recent
            <button class="copy-btn" onclick="copyTerminalContent('cf logs qianxiusg --recent', this)">复制</button>
        </div>
    </div>

## SPA美国教程

1.  **部署应用**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">部署美国应用</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf push qianxiuusa --docker-image ghcr.io/uncleluogithub/sapcf:latest -m 512M -k 512M --health-check-type port --no-route --no-start
            <button class="copy-btn" onclick="copyTerminalContent('cf push qianxiuusa --docker-image ghcr.io/uncleluogithub/sapcf:latest -m 512M -k 512M --health-check-type port --no-route --no-start', this)">复制</button>
        </div>
    </div>

2.  **设置环境参数**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">设置美国环境参数</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf set-env qianxiuusa APP_UUID 8a6e504e-70d4-4008-883e-67e746252ca8<br>
            <span class="terminal-prompt">$</span>cf set-env qianxiuusa VMESS_HOST spausa.0407123.xyz<br>
            <span class="terminal-prompt">$</span>cf set-env qianxiuusa TUNNEL_TOKEN 'eyJhIjoiMTlmOGI1NWVlOGY3NjA4ZmY0YzdmZGY2OTM0YzdmZDciLCJ0IjoiNDllMTY0MmEtOGZjMy00MjQzLTlhMDktNWNjZjQ4ZThhNTFkIiwicyI6Ik1tWTNaakUwTlRBdFpUWm1OaTAwT1RoaExUaGhNVGd0TkRWbE9XSTNNemc0WVdSaSJ9'
            <button class="copy-btn" onclick="copyTerminalContent('cf set-env qianxiuusa APP_UUID 8a6e504e-70d4-4008-883e-67e746252ca8\ncf set-env qianxiuusa VMESS_HOST spausa.0407123.xyz\ncf set-env qianxiuusa TUNNEL_TOKEN \'eyJhIjoiMTlmOGI1NWVlOGY3NjA4ZmY0YzdmZGY2OTM0YzdmZDciLCJ0IjoiNDllMTY0MmEtOGZjMy00MjQzLTlhMDktNWNjZjQ4ZThhNTFkIiwicyI6Ik1tWTNaakUwTlRBdFpUWm1OaTAwT1RoaExUaGhNVGd0TkRWbE9XSTNNemc0WVdSaSJ9\'', this)">复制</button>
        </div>
    </div>

3.  **确认端口健康检查类型**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">确认健康检查</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf set-health-check qianxiuusa port
            <button class="copy-btn" onclick="copyTerminalContent('cf set-health-check qianxiuusa port', this)">复制</button>
        </div>
    </div>

4.  **启动应用**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">启动美国应用</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf start qianxiuusa
            <button class="copy-btn" onclick="copyTerminalContent('cf start qianxiuusa', this)">复制</button>
        </div>
    </div>

5.  **查询节点信息（日志信息查看）**
    <div class="terminal-block">
        <div class="terminal-header">
            <span class="terminal-title">查询美国节点信息</span>
            <div class="terminal-buttons">
                <button class="terminal-btn close"></button>
                <button class="terminal-btn minimize"></button>
                <button class="terminal-btn maximize"></button>
            </div>
        </div>
        <div class="terminal-content">
            <span class="terminal-prompt">$</span>cf logs qianxiuusa --recent
            <button class="copy-btn" onclick="copyTerminalContent('cf logs qianxiuusa --recent', this)">复制</button>
        </div>
    </div>
